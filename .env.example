# API Keys - Choose one of the following options:

# Option 1: Gemini API (Google AI Studio)
# GEMINI_API_KEY=your_gemini_api_key_here

# Option 2: OpenRouter API (Recommended - supports multiple models)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=google/gemini-2.0-flash-001
OPENROUTER_SITE_URL=http://localhost:8000
OPENROUTER_SITE_NAME=PlanBook AI Service

# Service Configuration
API_PREFIX=/api/v1
DEBUG=True
PROJECT_NAME=PlanBook AI Service

# Security
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
