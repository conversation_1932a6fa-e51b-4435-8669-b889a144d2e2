#!/usr/bin/env python3
"""
Simple test to verify multi-lesson functionality
"""
import requests
import json

def test_single_vs_multi_lesson():
    """Compare single lesson vs multi lesson requests"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
    
    # Test 1: Single lesson (old behavior)
    single_lesson_request = {
        "exam_id": "test_single_001",
        "ten_truong": "Test School",
        "mon_hoc": "Chemistry",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Basic understanding",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    # Test 2: Multi lesson (new behavior)
    multi_lesson_request = {
        "exam_id": "test_multi_002",
        "ten_truong": "Test School",
        "mon_hoc": "Chemistry", 
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Basic understanding",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 1,
                        "loai_cau": ["TN"]
                    }
                ]
            },
            {
                "lesson_id": "235",
                "yeu_cau_can_dat": "Advanced understanding",
                "muc_do": [
                    {
                        "loai": "Thông hiểu",
                        "so_cau": 1,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("=== TESTING SINGLE vs MULTI LESSON ===")
    
    # Test single lesson
    print("\n1. Testing SINGLE lesson request...")
    print(f"   Lessons: {len(single_lesson_request['cau_hinh_de'])}")
    print(f"   Lesson IDs: {[c['lesson_id'] for c in single_lesson_request['cau_hinh_de']]}")
    
    try:
        response = requests.post(url, json=single_lesson_request, timeout=60)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', False)}")
            print(f"   File created: {result.get('filename', 'N/A')}")
        else:
            print(f"   Error: {response.text[:200]}")
    except Exception as e:
        print(f"   Failed: {e}")
    
    # Test multi lesson
    print("\n2. Testing MULTI lesson request...")
    print(f"   Lessons: {len(multi_lesson_request['cau_hinh_de'])}")
    print(f"   Lesson IDs: {[c['lesson_id'] for c in multi_lesson_request['cau_hinh_de']]}")
    
    try:
        response = requests.post(url, json=multi_lesson_request, timeout=60)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', False)}")
            print(f"   File created: {result.get('filename', 'N/A')}")
        else:
            print(f"   Error: {response.text[:200]}")
    except Exception as e:
        print(f"   Failed: {e}")
    
    print("\n=== CONCLUSION ===")
    print("If both tests return Status: 200 and Success: True,")
    print("then the multi-lesson functionality is working correctly!")

if __name__ == "__main__":
    test_single_vs_multi_lesson()
