#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để sửa format đáp án cho đề thi 20 câu
"""

def create_correct_answer_format():
    """Tạo format đáp án đúng cho 20 câu hỏi"""
    
    # Đáp án đúng cho 20 câu (dựa trên nội dung câu hỏi)
    correct_answers = {
        1: "B",   # Proton mang điện tích dương
        2: "C",   # Neutron không mang điện tích
        3: "C",   # Electron có khối lượng nhỏ nhất
        4: "C",   # Proton và Neutron nằm trong hạt nhân
        5: "C",   # Neutron có khối lượng ~1 amu và không mang điện
        6: "C",   # Proton và neutron trong hạt nhân, electron trong lớp vỏ
        7: "A",   # Z=16: 1s²2s²2p⁶3s²3p⁴
        8: "C",   # Na (Z=11) tạo Na⁺: 1s²2s²2p⁶
        9: "B",   # <PERSON><PERSON><PERSON> (Z=7) có 5 electron lớp ngoài cùng
        10: "B",  # 1s²2s²2p⁶3s² là Mg: chu kì 3, nhóm IIA
        11: "C",  # Cl⁻ (Z=17): 1s²2s²2p⁶3s²3p⁶
        12: "A",  # ns²np⁴ thuộc nhóm VIA
        13: "A",  # Nguyên tử gồm proton, neutron, electron
        14: "C",  # Proton mang điện tích dương (câu lặp)
        15: "C",  # Electron có khối lượng không đáng kể
        16: "C",  # Cần tính toán đồng vị (giả sử đáp án C)
        17: "A",  # Tính nguyên tử khối trung bình Mg
        18: "A",  # Tính % đồng vị ³⁵Cl
        19: "A",  # Tính nguyên tử khối trung bình Cl
        20: "B",  # Tính số proton từ đồng vị
    }
    
    print("ĐÁP ÁN")
    print()
    print("PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn. Thí sinh trả lời từ câu 1 đến câu 20")
    print("(Mỗi câu trả lời đúng thí sinh được 0,25 điểm)")
    print()
    
    # Tạo bảng đáp án 4 hàng x 6 cột (20 câu + 4 ô trống)
    print("┌─────┬─────┬─────┬─────┬─────┬─────┐")
    
    # Hàng 1: Câu 1-5
    print("│ Câu │  1  │  2  │  3  │  4  │  5  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    answers_1_5 = [correct_answers.get(i, "") for i in range(1, 6)]
    print(f"│Chọn │  {answers_1_5[0]}  │  {answers_1_5[1]}  │  {answers_1_5[2]}  │  {answers_1_5[3]}  │  {answers_1_5[4]}  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    
    # Hàng 2: Câu 6-10
    print("│ Câu │  6  │  7  │  8  │  9  │ 10  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    answers_6_10 = [correct_answers.get(i, "") for i in range(6, 11)]
    print(f"│Chọn │  {answers_6_10[0]}  │  {answers_6_10[1]}  │  {answers_6_10[2]}  │  {answers_6_10[3]}  │  {answers_6_10[4]}  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    
    # Hàng 3: Câu 11-15
    print("│ Câu │ 11  │ 12  │ 13  │ 14  │ 15  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    answers_11_15 = [correct_answers.get(i, "") for i in range(11, 16)]
    print(f"│Chọn │  {answers_11_15[0]}  │  {answers_11_15[1]}  │  {answers_11_15[2]}  │  {answers_11_15[3]}  │  {answers_11_15[4]}  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    
    # Hàng 4: Câu 16-20
    print("│ Câu │ 16  │ 17  │ 18  │ 19  │ 20  │")
    print("├─────┼─────┼─────┼─────┼─────┼─────┤")
    answers_16_20 = [correct_answers.get(i, "") for i in range(16, 21)]
    print(f"│Chọn │  {answers_16_20[0]}  │  {answers_16_20[1]}  │  {answers_16_20[2]}  │  {answers_16_20[3]}  │  {answers_16_20[4]}  │")
    print("└─────┴─────┴─────┴─────┴─────┴─────┘")
    
    print()
    print("=" * 50)
    print("FORMAT ĐÁP ÁN NGANG (dễ đọc hơn):")
    print("=" * 50)
    
    # Format ngang đơn giản
    for i in range(1, 21):
        answer = correct_answers.get(i, "?")
        print(f"Câu {i:2d}: {answer}", end="    ")
        if i % 5 == 0:  # Xuống dòng sau mỗi 5 câu
            print()
    
    print()
    print("=" * 50)
    print("FORMAT WORD TABLE (để copy vào Word):")
    print("=" * 50)
    
    # Format cho Word table
    print("Câu\t1\t2\t3\t4\t5")
    print("Chọn\t" + "\t".join([correct_answers.get(i, "") for i in range(1, 6)]))
    print()
    print("Câu\t6\t7\t8\t9\t10")
    print("Chọn\t" + "\t".join([correct_answers.get(i, "") for i in range(6, 11)]))
    print()
    print("Câu\t11\t12\t13\t14\t15")
    print("Chọn\t" + "\t".join([correct_answers.get(i, "") for i in range(11, 16)]))
    print()
    print("Câu\t16\t17\t18\t19\t20")
    print("Chọn\t" + "\t".join([correct_answers.get(i, "") for i in range(16, 21)]))

if __name__ == "__main__":
    create_correct_answer_format()
