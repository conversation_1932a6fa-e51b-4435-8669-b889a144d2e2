#!/usr/bin/env python3
"""
Test multi-lesson exam generation functionality
"""
import requests
import json

def test_multi_lesson_exam():
    """Test exam generation with multiple lessons"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
    
    # Request với nhiều lesson_id
    multi_lesson_request = {
        "exam_id": "test_multi_lesson_001",
        "ten_truong": "Truong THPT ABC",
        "mon_hoc": "Hoa hoc",
        "lop": 12,
        "tong_so_cau": 4,
        "cau_hinh_de": [
            {
                "lesson_id": "234",  # Lesson 1
                "yeu_cau_can_dat": "Hieu biet co ban ve hoa hoc",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 1,
                        "loai_cau": ["TN"]
                    }
                ]
            },
            {
                "lesson_id": "235",  # Lesson 2
                "yeu_cau_can_dat": "Van dung kien thuc hoa hoc",
                "muc_do": [
                    {
                        "loai": "Thông hiểu",
                        "so_cau": 1,
                        "loai_cau": ["TN"]
                    }
                ]
            },
            {
                "lesson_id": "236",  # Lesson 3
                "yeu_cau_can_dat": "Phan tich va danh gia",
                "muc_do": [
                    {
                        "loai": "Vận dụng",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("=== TESTING MULTI-LESSON EXAM GENERATION ===")
    print(f"Request contains {len(multi_lesson_request['cau_hinh_de'])} lessons:")
    for i, config in enumerate(multi_lesson_request['cau_hinh_de']):
        print(f"  Lesson {i+1}: ID={config['lesson_id']}, Questions={sum(m['so_cau'] for m in config['muc_do'])}")
    
    print("\nSending request...")
    try:
        response = requests.post(url, json=multi_lesson_request, timeout=120)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            
            # Analyze response
            if "questions" in result:
                questions = result["questions"]
                print(f"\nGenerated {len(questions)} questions total")
                
                # Check if questions are from different lessons
                lesson_sources = set()
                for q in questions:
                    if "metadata" in q and "lesson_id" in q["metadata"]:
                        lesson_sources.add(q["metadata"]["lesson_id"])
                
                print(f"Questions sourced from {len(lesson_sources)} different lessons: {list(lesson_sources)}")
                
                # Show first few questions
                print("\nFirst 2 questions:")
                for i, q in enumerate(questions[:2]):
                    print(f"  Q{i+1}: {q.get('cau_hoi', 'N/A')[:100]}...")
                    if "metadata" in q:
                        print(f"       Source: lesson_id={q['metadata'].get('lesson_id', 'N/A')}")
                
            else:
                print("WARNING: No questions found in response")
                print(f"Response keys: {list(result.keys())}")

        else:
            print(f"FAILED: {response.text}")

    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_multi_lesson_exam()
