#!/usr/bin/env python3
"""
Test atomic mass extraction and "Hết" position
"""
import requests
import json

def test_atomic_mass():
    """Test atomic mass and Hết position"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
    
    request_data = {
        "exam_id": "test_atomic_mass_v2",
        "ten_truong": "Test School",
        "mon_hoc": "Hóa học",  # Vietnamese chemistry
        "lop": 12,
        "tong_so_cau": 3,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Test atomic mass extraction",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 3,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("=== TESTING ATOMIC MASS & HET POSITION ===")
    print("Subject: Chemistry")
    print("Sending request...")
    
    try:
        response = requests.post(url, json=request_data, timeout=120)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"File created: {result.get('filename', 'N/A')}")
            print(f"Questions generated: {result.get('total_questions', 'N/A')}")
            
            # Check if atomic mass was extracted
            if 'additional_info' in result:
                print(f"Additional info: {result['additional_info']}")
                
        else:
            print(f"Error: {response.text[:500]}")
            
    except Exception as e:
        print(f"Failed: {e}")

if __name__ == "__main__":
    test_atomic_mass()
