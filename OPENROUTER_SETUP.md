# Hướng dẫn cấu hình OpenRouter API

## Tổng quan

PlanBook AI hiện đã hỗ trợ sử dụng OpenRouter API thay vì Gemini API trực tiếp. OpenRouter cung cấp:

- <PERSON><PERSON><PERSON> cập đến nhiều models LLM khác nhau
- G<PERSON><PERSON> cả cạnh tranh và linh hoạt
- Không bị giới hạn quota như Gemini API miễn phí
- Hỗ trợ nhiều models: GPT-4, <PERSON>, <PERSON>, và nhiều models khác

## Cách cấu hình

### 1. Tạo tài khoản OpenRouter

1. <PERSON><PERSON><PERSON> cập [OpenRouter.ai](https://openrouter.ai/)
2. Đăng ký tài khoản mới
3. Vào phần **Keys** để tạo API key mới

### 2. Cấu hình trong file .env

Thay thế cấu hình Gemini bằng OpenRouter:

```env
# Tắt Gemini API (comment out)
# GEMINI_API_KEY=your_gemini_key_here

# Cấu hình OpenRouter API
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=google/gemini-2.0-flash-001
OPENROUTER_SITE_URL=http://localhost:8000
OPENROUTER_SITE_NAME=PlanBook AI Service
```

### 3. Models được hỗ trợ

Bạn có thể thay đổi `OPENROUTER_MODEL` để sử dụng các models khác:

- `google/gemini-2.0-flash-001` - Gemini 2.0 Flash (mặc định)
- `google/gemini-1.5-pro` - Gemini 1.5 Pro
- `anthropic/claude-3-sonnet` - Claude 3 Sonnet
- `openai/gpt-4o` - GPT-4 Omni
- `meta-llama/llama-3.1-70b-instruct` - Llama 3.1 70B

Xem danh sách đầy đủ tại: [OpenRouter Models](https://openrouter.ai/models)

## Kiểm tra cấu hình

Chạy script test để kiểm tra:

```bash
python test_openrouter_api.py
```

Kết quả mong đợi:
```
Starting OpenRouter API Tests...
=== Testing OpenRouter Service ===
API Key: sk-or-v1-7e6065a2d3c...
Model: google/gemini-2.0-flash-001
Available: True

--- Testing Connection ---
Connection success: True

--- Testing Content Generation ---
Generation success: True
Response length: 373 characters
Token usage: {'prompt_tokens': 10, 'completion_tokens': 132, 'total_tokens': 142}
OpenRouter service test passed!

=== Testing LLM Service with OpenRouter ===
Use OpenRouter: True
Available: True

--- Testing Exam Question Generation ---
Generation success: True
Response length: 296 characters
LLM service test passed!

=== Test Summary ===
OpenRouter Service: PASS
LLM Service: PASS

All tests passed! OpenRouter API is working correctly.
```

## Ưu điểm của OpenRouter

1. **Không giới hạn quota**: Không bị giới hạn như Gemini API miễn phí
2. **Nhiều lựa chọn model**: Có thể chuyển đổi giữa các models khác nhau
3. **Giá cả minh bạch**: Trả theo usage thực tế
4. **Hiệu suất ổn định**: Không bị gián đoạn do quota
5. **Dễ dàng scale**: Phù hợp cho production

## Troubleshooting

### Lỗi "API key not found"
- Kiểm tra file `.env` có chứa `OPENROUTER_API_KEY`
- Đảm bảo API key đúng format: `sk-or-v1-...`

### Lỗi "Model not found"
- Kiểm tra tên model trong `OPENROUTER_MODEL`
- Xem danh sách models hỗ trợ tại OpenRouter

### Lỗi "Insufficient credits"
- Kiểm tra balance trong tài khoản OpenRouter
- Nạp thêm credits nếu cần

## Chuyển đổi từ Gemini sang OpenRouter

Hệ thống sẽ tự động ưu tiên OpenRouter nếu có `OPENROUTER_API_KEY`. Nếu không có, sẽ fallback về Gemini API.

Để chuyển đổi hoàn toàn:
1. Thêm cấu hình OpenRouter vào `.env`
2. Comment out `GEMINI_API_KEY`
3. Restart server
4. Chạy test để kiểm tra

## Giá cả tham khảo

- Gemini 2.0 Flash: ~$0.075/1M tokens input, ~$0.30/1M tokens output
- Gemini 1.5 Pro: ~$1.25/1M tokens input, ~$5.00/1M tokens output
- GPT-4 Omni: ~$2.50/1M tokens input, ~$10.00/1M tokens output

Xem giá cả cập nhật tại: [OpenRouter Pricing](https://openrouter.ai/models)
