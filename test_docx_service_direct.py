#!/usr/bin/env python3
"""
Test script để kiểm tra ExamDocxService trực tiếp
"""
import sys
import os
import traceback
import asyncio

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_exam_docx_service():
    """Test ExamDocxService trực tiếp"""
    try:
        print("=== Testing ExamDocxService Directly ===")
        
        # Import service
        from app.services.exam_docx_service import ExamDocxService
        
        print("[OK] ExamDocxService imported successfully")

        # Tạo service instance
        service = ExamDocxService()
        print("[OK] ExamDocxService instance created")
        
        # Tạo fake exam data
        exam_request = {
            "exam_id": "test_exam_001",
            "ten_truong": "Truong THPT Test",
            "mon_hoc": "Hoa hoc",
            "lop": 12,
            "tong_so_cau": 2
        }
        
        exam_data = {
            "questions": [
                {
                    "question": "Cau hoi 1: <PERSON>uyen tu la gi?",
                    "options": ["A. Dap an A", "B. Dap an B", "C. Dap an C", "D. Dap an D"],
                    "correct_answer": "A",
                    "explanation": "Giai thich cau 1"
                },
                {
                    "question": "Cau hoi 2: Electron la gi?", 
                    "options": ["A. Dap an A", "B. Dap an B", "C. Dap an C", "D. Dap an D"],
                    "correct_answer": "B",
                    "explanation": "Giai thich cau 2"
                }
            ]
        }
        
        print("[OK] Test data created")

        # Test create_exam_docx
        print("\nTesting create_exam_docx method...")
        result = await service.create_exam_docx(exam_request, exam_data)

        print(f"Result: {result}")

        if result.get("success"):
            print("[OK] create_exam_docx succeeded!")

            # Kiểm tra file có tồn tại không
            filepath = result.get("filepath")
            if filepath and os.path.exists(filepath):
                print(f"[OK] DOCX file created: {filepath}")
                file_size = os.path.getsize(filepath)
                print(f"[OK] File size: {file_size} bytes")

                # Cleanup
                try:
                    os.remove(filepath)
                    print("[OK] Test file cleaned up")
                except:
                    print("[WARNING] Could not clean up test file")

                return True
            else:
                print("[ERROR] DOCX file not found")
                return False
        else:
            print(f"[ERROR] create_exam_docx failed: {result.get('error')}")
            return False

    except Exception as e:
        print(f"[ERROR] Error during test: {e}")
        print(f"Error type: {type(e)}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def test_sanitize_function():
    """Test hàm sanitize riêng"""
    try:
        print("\n=== Testing Sanitize Function ===")
        
        from app.services.exam_docx_service import ExamDocxService
        
        service = ExamDocxService()
        
        test_cases = [
            "Đề thi Hóa học lớp 12",
            "test_exam_001",
            "exam!@#$%^&*()",
            "a" * 200,
            "",
            "   "
        ]
        
        for original in test_cases:
            try:
                sanitized = service._sanitize_filename(original)
                print(f"'{original}' -> '{sanitized}'")
            except Exception as e:
                print(f"Error sanitizing '{original}': {e}")
                
        return True
        
    except Exception as e:
        print(f"Error testing sanitize: {e}")
        return False

if __name__ == "__main__":
    print("Starting direct ExamDocxService tests...")
    
    # Test 1: Sanitize function
    sanitize_ok = test_sanitize_function()
    
    # Test 2: Full service
    service_ok = asyncio.run(test_exam_docx_service())
    
    print(f"\n=== Results ===")
    print(f"Sanitize test: {'PASS' if sanitize_ok else 'FAIL'}")
    print(f"Service test: {'PASS' if service_ok else 'FAIL'}")
    
    if service_ok:
        print("\n[SUCCESS] ExamDocxService works correctly!")
        print("The errno 22 issue might be elsewhere in the API flow.")
    else:
        print("\n[FAILED] ExamDocxService has issues!")
        print("The errno 22 issue is in the DOCX creation logic.")
