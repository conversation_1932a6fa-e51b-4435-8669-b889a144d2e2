#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra format đáp án động
"""

import asyncio
import sys
import os

# Thêm thư mục gốc vào Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.exam_docx_service import ExamDocxService

async def test_dynamic_answer_format():
    """Test tạo đáp án động cho số câu khác nhau"""
    
    service = ExamDocxService()
    
    # Test case 1: 20 câu hỏi (như ví dụ của bạn)
    test_questions_20 = []
    for i in range(1, 21):
        question = {
            "loai_cau": "TN",
            "cau_hoi": f"Câu hỏi số {i}?",
            "dap_an": {
                "A": f"Đáp án A cho câu {i}",
                "B": f"Đ<PERSON><PERSON> án B cho câu {i}",
                "C": f"Đáp án C cho câu {i}",
                "D": f"Đáp án D cho câu {i}",
                "dung": ["A", "B", "C", "D"][i % 4]  # Xoay vòng A,B,C,D
            },
            "giai_thich": f"Giải thích cho câu {i}"
        }
        test_questions_20.append(question)
    
    # Test case 2: 40 câu hỏi
    test_questions_40 = []
    for i in range(1, 41):
        question = {
            "loai_cau": "TN",
            "cau_hoi": f"Câu hỏi số {i}?",
            "dap_an": {
                "A": f"Đáp án A cho câu {i}",
                "B": f"Đáp án B cho câu {i}",
                "C": f"Đáp án C cho câu {i}",
                "D": f"Đáp án D cho câu {i}",
                "dung": ["A", "B", "C", "D"][i % 4]  # Xoay vòng A,B,C,D
            },
            "giai_thich": f"Giải thích cho câu {i}"
        }
        test_questions_40.append(question)
    
    # Test case 3: 5 câu hỏi (ít câu)
    test_questions_5 = []
    for i in range(1, 6):
        question = {
            "loai_cau": "TN",
            "cau_hoi": f"Câu hỏi số {i}?",
            "dap_an": {
                "A": f"Đáp án A cho câu {i}",
                "B": f"Đáp án B cho câu {i}",
                "C": f"Đáp án C cho câu {i}",
                "D": f"Đáp án D cho câu {i}",
                "dung": ["A", "B", "C", "D"][i % 4]  # Xoay vòng A,B,C,D
            },
            "giai_thich": f"Giải thích cho câu {i}"
        }
        test_questions_5.append(question)
    
    # Exam request mau
    exam_request = {
        "mon_hoc": "Hoa hoc",
        "lop": "12",
        "ten_truong": "Truong THPT Nguyen Hue",
        "thoi_gian": "45 phut"
    }
    
    # Test cases
    test_cases = [
        ("20_cau", test_questions_20, "De thi 20 cau"),
        ("40_cau", test_questions_40, "De thi 40 cau"),
        ("5_cau", test_questions_5, "De thi 5 cau")
    ]
    
    for case_name, questions, description in test_cases:
        print(f"\n{'='*50}")
        print(f"Testing: {description}")
        print(f"So cau hoi: {len(questions)}")
        print(f"{'='*50}")
        
        # Tạo exam data
        exam_data = {
            "questions": questions,
            "exam_id": f"test_{case_name}",
            "success": True
        }
        
        try:
            # Tạo DOCX
            result = await service.create_exam_docx(
                exam_data=exam_data,
                exam_request=exam_request
            )
            
            if result.get("success"):
                print(f"[OK] Tao file thanh cong: {result.get('filename')}")
                print(f"[PATH] Duong dan: {result.get('filepath')}")
                print(f"[SIZE] Kich thuoc: {result.get('file_size')} bytes")

                # Kiểm tra file có tồn tại không
                filepath = result.get('filepath')
                if filepath and os.path.exists(filepath):
                    print(f"[OK] File ton tai va co the mo duoc")
                else:
                    print(f"[ERROR] File khong ton tai")
            else:
                print(f"[ERROR] Tao file that bai: {result.get('error')}")

        except Exception as e:
            print(f"[ERROR] Loi khi tao file: {e}")
    
    print(f"\n{'='*50}")
    print("Test hoan thanh!")
    print("Kiem tra cac file DOCX da tao de xem format dap an dong")
    print(f"{'='*50}")

if __name__ == "__main__":
    asyncio.run(test_dynamic_answer_format())
