#!/usr/bin/env python3
"""
Test script để kiểm tra hàm sanitize filename
"""
import re
import unicodedata

def sanitize_filename(filename: str) -> str:
    """
    Làm sạch filename để tránh lỗi encoding và giới hạn độ dài
    """
    try:
        # Chuyển đổi ký tự tiếng Việt sang ASCII
        # Normalize unicode và loại bỏ dấu
        normalized = unicodedata.normalize('NFD', filename)
        ascii_only = normalized.encode('ascii', 'ignore').decode('ascii')
        
        # Loại bỏ ký tự đặc biệt, chỉ giữ lại chữ cái, số, dấu gạch dưới và gạch ngang
        sanitized = re.sub(r"[^\w\-_]", "_", ascii_only)

        # Loại bỏ nhiều dấu gạch dưới liên tiếp
        sanitized = re.sub(r"_+", "_", sanitized)

        # Loại bỏ dấu gạch dưới ở đầu và cuối
        sanitized = sanitized.strip("_")

        # Giới hạn độ dài tên file (Windows có giới hạn 255 ký tự cho tên file)
        # Để an toàn, giới hạn ở 100 ký tự
        if len(sanitized) > 100:
            sanitized = sanitized[:100]

        # Nếu kết quả rỗng, dùng default
        if not sanitized:
            sanitized = "lesson"

        return sanitized

    except Exception as e:
        print(f"Error sanitizing filename '{filename}': {e}")
        return "lesson"

def test_sanitize():
    """Test các trường hợp khác nhau"""
    test_cases = [
        "Hóa học",
        "test_exam_001", 
        "Đề thi Hóa học lớp 12",
        "exam_with_special_chars!@#$%^&*()",
        "a" * 200,  # Tên dài
        "Môn Hóa học - Lớp 12 - Đề số 1",
        "",
        "   ",
        "Thi_thử_THPT_2024_Hóa_học"
    ]
    
    print("=== Testing filename sanitization ===")
    for i, original in enumerate(test_cases):
        sanitized = sanitize_filename(original)
        try:
            print(f"Test {i+1}: '{original}' ({len(original)} chars)")
        except UnicodeEncodeError:
            print(f"Test {i+1}: [Unicode string] ({len(original)} chars)")
        print(f"Sanitized: '{sanitized}' ({len(sanitized)} chars)")
        print("---")

if __name__ == "__main__":
    test_sanitize()
