#!/usr/bin/env python3
"""
Test script để tạo exam và giữ file DOCX để kiểm tra
"""
import requests
import json
import time
import os

def test_exam_and_keep_file():
    """Test exam generation và giữ file để kiểm tra"""
    
    url = "http://localhost:8006/api/v1/exam/generate-exam-test"
    
    payload = {
        "exam_id": "hoa12_de_2cau_test",
        "ten_truong": "Trường THPT Test",
        "mon_hoc": "Hóa học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Hiểu và phân biệt proton, neutron, electron theo khối lư<PERSON>, điện tích và vị trí.",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("Testing exam generation API...")
    print(f"URL: {url}")
    
    try:
        # Gọi API
        response = requests.post(url, json=payload, timeout=60)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API hoạt động bình thường!")
            
            # Lưu response content vào file
            filename = f"test_exam_{int(time.time())}.docx"
            with open(filename, "wb") as f:
                f.write(response.content)
            
            print(f"✅ File DOCX đã được lưu: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Kiểm tra nội dung file ngay lập tức
            check_docx_content(filename)
            
        else:
            print(f"❌ API trả về lỗi: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Lỗi khi test API: {e}")

def check_docx_content(filename):
    """Kiểm tra nội dung file DOCX"""
    try:
        from docx import Document
        
        print(f"\n=== KIỂM TRA NỘI DUNG FILE: {filename} ===")
        
        doc = Document(filename)
        
        question_count = 0
        empty_questions = 0
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:
                print(f"Paragraph {i+1}: {text}")
                
                # Đếm câu hỏi
                if text.startswith("Câu "):
                    question_count += 1
                    print(f"  -> QUESTION {question_count} DETECTED")
                    
                    # Kiểm tra câu hỏi trống
                    if text.endswith(":") and len(text.split(":")[1].strip()) == 0:
                        empty_questions += 1
                        print(f"  -> ❌ EMPTY QUESTION!")
                    else:
                        print(f"  -> ✅ Question has content")
        
        print(f"\n=== KẾT QUẢ ===")
        print(f"Tổng số câu hỏi: {question_count}")
        print(f"Câu hỏi trống: {empty_questions}")
        
        if empty_questions == 0:
            print("🎉 TẤT CẢ CÂU HỎI ĐỀU CÓ NỘI DUNG!")
        else:
            print(f"⚠️ CÓ {empty_questions} CÂU HỎI TRỐNG!")
            
    except Exception as e:
        print(f"❌ Lỗi khi đọc file DOCX: {e}")

if __name__ == "__main__":
    test_exam_and_keep_file()
