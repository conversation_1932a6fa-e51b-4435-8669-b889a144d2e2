#!/usr/bin/env python3
"""
Simple test to verify the fix
"""

def test_field_mapping():
    """Test field mapping logic"""
    
    print("=== TESTING FIELD MAPPING FIX ===")
    
    # Simulate Gemini response
    gemini_data = {
        "cau_hoi": "Test question content",
        "dap_an": {"A": "Answer A", "B": "Answer B", "C": "Answer C", "D": "Answer D"},
        "dap_an_dung": "A",
        "giai_thich": "Test explanation",
        "muc_do": "Nhan biet",
        "loai_cau": "TN"
    }
    
    print("1. Gemini response data:")
    print(f"   cau_hoi: '{gemini_data.get('cau_hoi')}'")
    
    # OLD METHOD (BROKEN) - _generate_questions_for_lesson_with_llm
    old_question = {
        "cau_hoi": gemini_data.get("cau_hoi", ""),  # Wrong field name
        "dap_an": gemini_data.get("dap_an", {}),
    }
    
    # NEW METHOD (FIXED) - consistent field mapping
    new_question = {
        "noi_dung_cau_hoi": gemini_data.get("cau_hoi", ""),  # Correct field name
        "dap_an": gemini_data.get("dap_an", {}),
    }
    
    print("\n2. OLD method (broken):")
    print(f"   question['cau_hoi']: '{old_question.get('cau_hoi')}'")
    print(f"   question['noi_dung_cau_hoi']: '{old_question.get('noi_dung_cau_hoi')}'")
    
    print("\n3. NEW method (fixed):")
    print(f"   question['cau_hoi']: '{new_question.get('cau_hoi')}'")
    print(f"   question['noi_dung_cau_hoi']: '{new_question.get('noi_dung_cau_hoi')}'")
    
    # DOCX service reads 'noi_dung_cau_hoi'
    print("\n4. DOCX service reads:")
    old_docx_content = old_question.get("noi_dung_cau_hoi", "")
    new_docx_content = new_question.get("noi_dung_cau_hoi", "")
    
    print(f"   OLD: '{old_docx_content}' -> {'EMPTY!' if not old_docx_content else 'HAS CONTENT!'}")
    print(f"   NEW: '{new_docx_content}' -> {'EMPTY!' if not new_docx_content else 'HAS CONTENT!'}")
    
    print("\n=== RESULT ===")
    if new_docx_content and not old_docx_content:
        print("SUCCESS: Fix resolved the empty question issue!")
        return True
    else:
        print("FAILED: Fix did not work!")
        return False

if __name__ == "__main__":
    success = test_field_mapping()
    if success:
        print("\nThe fix should resolve the empty question content issue.")
        print("Questions will now appear properly in DOCX files.")
    else:
        print("\nThe fix needs more work.")
