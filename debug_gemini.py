#!/usr/bin/env python3
"""
Debug script để test trực tiếp Gemini API và xem response format
"""
import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_question_generation():
    """Test Gemini API để tạo câu hỏi và xem response format"""
    
    # Configure Gemini
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("GEMINI_API_KEY not found in .env")
        return
    
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-1.5-flash-latest')
    
    # Simple prompt để tạo 2 câu hỏi
    prompt = """
Tạo 2 câu hỏi trắc nghiệm về hóa học theo format JSON sau:

```json
[
  {
    "cau_hoi": "Nội dung câu hỏi ở đây",
    "dap_an": {
      "A": "Lựa chọn A",
      "B": "Lựa chọn B", 
      "C": "Lựa chọn C",
      "D": "Lựa chọn D"
    },
    "dap_an_dung": "A",
    "giai_thich": "Giải thích đáp án",
    "muc_do": "Nhận biết",
    "loai_cau": "TN"
  }
]
```

Chủ đề: Cấu tạo nguyên tử - proton, neutron, electron
Mức độ: Nhận biết
Số câu: 2

Chỉ trả về JSON array, không thêm text khác.
"""

    try:
        print("Calling Gemini API...")
        response = model.generate_content(prompt)

        print("Response received!")

        # Save response to file
        with open("gemini_response.txt", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("Response saved to gemini_response.txt")

        print("=" * 50)
        
        # Test parsing
        import re
        import json
        
        # Tìm JSON blocks
        json_pattern = r'```json\s*(\[.*?\])\s*```'
        matches = re.findall(json_pattern, response.text, re.DOTALL)
        
        print(f"Found {len(matches)} JSON blocks")
        
        if matches:
            for i, match in enumerate(matches):
                print(f"\nJSON Block {i+1}:")
                # Save JSON block to file too
                with open(f"json_block_{i+1}.txt", "w", encoding="utf-8") as f:
                    f.write(match)
                print(f"JSON block saved to json_block_{i+1}.txt")
                
                try:
                    questions = json.loads(match)
                    print(f"Successfully parsed {len(questions)} questions")
                    
                    for j, q in enumerate(questions):
                        print(f"\nQuestion {j+1}:")
                        print(f"  cau_hoi: '{q.get('cau_hoi', 'MISSING')}'")
                        print(f"  dap_an: {q.get('dap_an', 'MISSING')}")
                        print(f"  dap_an_dung: '{q.get('dap_an_dung', 'MISSING')}'")
                        
                except json.JSONDecodeError as e:
                    print(f"JSON parse error: {e}")
        else:
            print("No JSON blocks found in response")
            
            # Try direct JSON extraction
            start_idx = response.text.find("[")
            if start_idx != -1:
                print("Trying direct JSON extraction...")
                bracket_count = 0
                end_idx = -1
                for i in range(start_idx, len(response.text)):
                    if response.text[i] == '[':
                        bracket_count += 1
                    elif response.text[i] == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_idx = i
                            break
                
                if end_idx != -1:
                    json_text = response.text[start_idx:end_idx + 1]
                    print(f"Extracted JSON: {json_text}")
                    
                    try:
                        questions = json.loads(json_text)
                        print(f"Direct extraction successful: {len(questions)} questions")
                    except json.JSONDecodeError as e:
                        print(f"Direct extraction failed: {e}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_gemini_question_generation()
