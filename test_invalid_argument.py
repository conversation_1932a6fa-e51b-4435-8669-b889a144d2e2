#!/usr/bin/env python3
"""
Test script để kiểm tra xem còn lỗi Invalid argument không
"""
import requests

def test_exam_invalid_argument():
    """Test exam generation để kiểm tra lỗi Invalid argument"""
    try:
        print("Testing exam generation for Invalid argument error...")
        
        url = "http://localhost:8000/api/v1/exam/generate-exam"
        
        payload = {
            "exam_id": "test_exam_fake",
            "ten_truong": "Truong THPT Test",
            "mon_hoc": "Hoa hoc",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "lesson_id": "fake_lesson_123",
                    "yeu_cau_can_dat": "Test requirement",
                    "muc_do": [
                        { 
                            "loai": "Nhận biết", 
                            "so_cau": 2, 
                            "loai_cau": ["TN"] 
                        }
                    ]
                }
            ]
        }
        
        print(f"URL: {url}")
        print("Payload created successfully")
        
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        
        response_text = response.text
        print(f"Response length: {len(response_text)}")
        
        # Kiểm tra lỗi Invalid argument
        if "Invalid argument" in response_text:
            print("[ERROR] Still getting Invalid argument error!")
            print("Response contains: Invalid argument")
            return False
        elif response.status_code == 500:
            print(f"[INFO] Got 500 error but not Invalid argument")
            # In ra một phần response để debug
            if len(response_text) > 200:
                print(f"Response preview: {response_text[:200]}...")
            else:
                print(f"Full response: {response_text}")
            return True
        elif response.status_code == 404:
            print("[INFO] Got 404 - lesson not found (expected)")
            print(f"Response: {response_text}")
            return True
        else:
            print(f"[INFO] Got status {response.status_code}")
            print(f"Response: {response_text}")
            return True
        
    except Exception as e:
        print(f"Error during request: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing for Invalid Argument Error ===")
    
    success = test_exam_invalid_argument()
    
    if success:
        print("\n[SUCCESS] No Invalid argument error detected!")
        print("The errno 22 issue appears to be resolved.")
    else:
        print("\n[FAILED] Invalid argument error still exists!")
        print("Need to investigate further.")
