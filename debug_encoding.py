#!/usr/bin/env python3
"""
Debug encoding issues
"""
import requests
import json
import sys
import os

def test_simple_request():
    """Test với request đơn giản"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
    
    # Request đơn giản với ASCII only
    simple_request = {
        "exam_id": "test_ascii_001",
        "ten_truong": "Test School",
        "mon_hoc": "Chemistry",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Test requirement",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("Testing with ASCII-only request...")
    try:
        response = requests.post(url, json=simple_request, timeout=30)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Error: {response.text}")
        else:
            print("Success!")
            
    except Exception as e:
        print(f"Request failed: {e}")

def test_vietnamese_request():
    """Test với request có tiếng Việt"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
    
    # Request có tiếng Việt
    vietnamese_request = {
        "exam_id": "test_vietnamese_001",
        "ten_truong": "Trường THPT Nguyễn Huệ",
        "mon_hoc": "Hóa học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Hiểu và phân biệt proton, neutron, electron",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    print("Testing with Vietnamese request...")
    try:
        response = requests.post(url, json=vietnamese_request, timeout=30)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Error: {response.text}")
        else:
            print("Success!")
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("=== ENCODING DEBUG TEST ===")
    print(f"Python encoding: {sys.stdout.encoding}")
    print(f"File system encoding: {sys.getfilesystemencoding()}")
    print(f"Default encoding: {sys.getdefaultencoding()}")
    
    print("\n1. Testing ASCII request...")
    test_simple_request()
    
    print("\n2. Testing Vietnamese request...")
    test_vietnamese_request()
