#!/usr/bin/env python3
"""
Test script để tìm lessons có sẵn trong database
"""
import requests

def test_search_all():
    """Test search trong tất cả textbooks"""
    try:
        print("Testing search all textbooks...")
        
        url = "http://localhost:8000/api/v1/pdf/search"
        params = {"query": "hoa", "limit": 5}
        
        print(f"URL: {url}")
        print(f"Params: {params}")
        
        response = requests.get(url, params=params)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Search results: {data}")
            
            # Tìm lesson_id từ kết quả
            if 'results' in data and len(data['results']) > 0:
                first_result = data['results'][0]
                print(f"First result: {first_result}")
                
                # Tìm lesson_id trong metadata
                if 'metadata' in first_result:
                    metadata = first_result['metadata']
                    if 'lesson_id' in metadata:
                        return metadata['lesson_id']
                    elif 'lesson' in metadata:
                        return metadata['lesson']
                        
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

def test_get_all_textbooks():
    """Test lấy tất cả textbooks"""
    try:
        print("\nTesting get all textbooks...")
        
        url = "http://localhost:8000/api/v1/pdf/getAllTextBook"
        
        print(f"URL: {url}")
        
        response = requests.get(url)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Textbooks: {data}")
            
            # Tìm lesson_id từ textbooks
            if 'textbooks' in data and len(data['textbooks']) > 0:
                for textbook in data['textbooks']:
                    if 'chapters' in textbook:
                        for chapter in textbook['chapters']:
                            if 'lessons' in chapter and len(chapter['lessons']) > 0:
                                first_lesson = chapter['lessons'][0]
                                if 'lesson_id' in first_lesson:
                                    print(f"Found lesson_id: {first_lesson['lesson_id']}")
                                    return first_lesson['lesson_id']
                                elif 'id' in first_lesson:
                                    print(f"Found lesson id: {first_lesson['id']}")
                                    return first_lesson['id']
                        
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

def test_exam_with_real_lesson_id(lesson_id):
    """Test exam generation với lesson_id thực"""
    try:
        print(f"\nTesting exam generation with real lesson_id: {lesson_id}")
        
        url = "http://localhost:8000/api/v1/exam/generate-exam"
        
        payload = {
            "exam_id": "test_exam_real",
            "ten_truong": "Truong THPT Test",
            "mon_hoc": "Hoa hoc",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "lesson_id": lesson_id,
                    "yeu_cau_can_dat": "Hieu biet co ban ve cau tao nguyen tu",
                    "muc_do": [
                        { 
                            "loai": "Nhận biết", 
                            "so_cau": 2, 
                            "loai_cau": ["TN"] 
                        }
                    ]
                }
            ]
        }
        
        print(f"Payload: {payload}")
        
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 500 and "Invalid argument" in response.text:
            print("[ERROR] Still getting Invalid argument error!")
            return False
        elif response.status_code == 200:
            print("[SUCCESS] Exam generation worked!")
            return True
        else:
            print(f"[INFO] Got different error: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("=== Finding Real Lesson IDs ===")
    
    # Test 1: Search
    lesson_id = test_search_all()
    
    # Test 2: Get all textbooks
    if not lesson_id:
        lesson_id = test_get_all_textbooks()
    
    # Test 3: Test exam generation
    if lesson_id:
        print(f"\nFound lesson_id: {lesson_id}")
        success = test_exam_with_real_lesson_id(lesson_id)
        if success:
            print("[OK] Exam generation successful!")
        else:
            print("[ERROR] Exam generation failed!")
    else:
        print("[ERROR] No lesson_id found in database!")
        
        # Test với lesson_id giả để xem có còn lỗi Invalid argument không
        print("\nTesting with fake lesson_id to check for Invalid argument error...")
        test_exam_with_real_lesson_id("fake_lesson_123")
