#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra API
"""
import requests

def test_simple_api():
    """Test API đơn giản"""
    try:
        # Test root endpoint
        print("Testing root endpoint...")
        response = requests.get("http://localhost:8000/", timeout=10)
        print(f"Root endpoint - Status: {response.status_code}")

        if response.status_code == 200:
            print("Testing health check...")
            response = requests.get("http://localhost:8000/api/v1/pdf/health", timeout=10)
            print(f"Health check - Status: {response.status_code}")
        
        if response.status_code == 200:
            print("Server is running!")
            return True
        else:
            print("Server not responding properly")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_simple_api()
