#!/usr/bin/env python3
"""
Check available lesson IDs in the database
"""
import requests
import json

def check_available_lessons():
    """Check what lesson IDs are available"""
    
    # Test different lesson IDs
    test_lesson_ids = ["234", "235", "236", "237", "238", "239", "240", "241", "242", "243"]
    
    print("=== CHECKING AVAILABLE LESSON IDs ===")
    
    available_lessons = []
    
    for lesson_id in test_lesson_ids:
        url = f"http://127.0.0.1:8000/api/v1/lessons/{lesson_id}/content"
        
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success', False):
                    content = data.get('content', {})
                    lesson_info = content.get('lesson_info', {})
                    lesson_title = lesson_info.get('lesson_title', 'Unknown')
                    chapter_title = lesson_info.get('chapter_title', 'Unknown')
                    
                    print(f"✅ Lesson {lesson_id}: {lesson_title} (Chapter: {chapter_title})")
                    available_lessons.append({
                        'lesson_id': lesson_id,
                        'title': lesson_title,
                        'chapter': chapter_title
                    })
                else:
                    print(f"❌ Lesson {lesson_id}: No content")
            else:
                print(f"❌ Lesson {lesson_id}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Lesson {lesson_id}: Error - {e}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Available lessons: {len(available_lessons)}")
    
    if available_lessons:
        print("\nRecommended lesson_ids for your exam:")
        for lesson in available_lessons[:5]:  # Show first 5
            print(f"  - {lesson['lesson_id']}: {lesson['title']}")
    
    return available_lessons

if __name__ == "__main__":
    check_available_lessons()
