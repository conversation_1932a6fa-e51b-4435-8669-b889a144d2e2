#!/usr/bin/env python3
"""
Test script để mô phỏng fix và kiểm tra logic
"""
import sys
import os
from docx import Document

# Add app to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def simulate_question_processing():
    """Mô phỏng quá trình xử lý câu hỏi sau khi fix"""
    
    print("=== SIMULATION: Question Processing After Fix ===")
    
    # D<PERSON> liệu từ Gemini (giống nh<PERSON> thật)
    gemini_response_data = [
        {
            "cau_hoi": "Hạt nào mang điện tích dương trong nguyên tử?",
            "dap_an": {
                "A": "Proton",
                "B": "Neutron",
                "C": "Electron", 
                "D": "Proton và Neutron"
            },
            "dap_an_dung": "A",
            "giai_thich": "Proton mang điện tích dư<PERSON>, neutron không mang điện, electron mang điện tích âm.",
            "muc_do": "Nhận biết",
            "loai_cau": "TN"
        },
        {
            "cau_hoi": "Hạt nào có khối lượng gần bằng 0 so với proton và neutron trong nguyên tử?",
            "dap_an": {
                "A": "Proton",
                "B": "Neutron",
                "C": "Electron",
                "D": "Proton và Neutron"
            },
            "dap_an_dung": "C", 
            "giai_thich": "Khối lượng của electron rất nhỏ so với proton và neutron.",
            "muc_do": "Nhận biết",
            "loai_cau": "TN"
        }
    ]
    
    print(f"Gemini response: {len(gemini_response_data)} questions")
    
    # Mô phỏng logic trong exam_generation_service.py (AFTER FIX)
    formatted_questions = []
    for i, q_data in enumerate(gemini_response_data):
        question = {
            "stt": i + 1,
            "loai_cau": q_data.get("loai_cau", "TN"),
            "muc_do": q_data.get("muc_do", "Nhận biết"),
            "noi_dung_cau_hoi": q_data.get("cau_hoi", ""),  # ✅ FIX: sử dụng field nhất quán
            "dap_an": q_data.get("dap_an", {}),
            "giai_thich": q_data.get("giai_thich", ""),
            "bai_hoc": "Test Lesson",
            "noi_dung_kien_thuc": "Test Content",
        }
        formatted_questions.append(question)
        
        print(f"\nQuestion {i+1} after formatting:")
        print(f"  noi_dung_cau_hoi: '{question['noi_dung_cau_hoi']}'")
        print(f"  dap_an: {question['dap_an']}")
        print(f"  loai_cau: {question['loai_cau']}")
    
    # Mô phỏng logic trong exam_docx_service.py
    print(f"\n=== SIMULATION: DOCX Generation ===")
    
    for i, question in enumerate(formatted_questions, 1):
        # Logic từ _create_single_question method
        loai_cau = question.get("loai_cau", "")
        dap_an = question.get("dap_an", {})
        noi_dung_cau_hoi = question.get("noi_dung_cau_hoi", "")  # ✅ Đọc đúng field
        
        print(f"\nDOCX Question {i}:")
        print(f"  Reading field 'noi_dung_cau_hoi': '{noi_dung_cau_hoi}'")
        print(f"  loai_cau: '{loai_cau}'")
        print(f"  dap_an keys: {list(dap_an.keys()) if dap_an else 'None'}")
        
        # Mô phỏng tạo paragraph trong DOCX
        if noi_dung_cau_hoi:
            docx_text = f"Câu {i}: {noi_dung_cau_hoi}"
            print(f"  DOCX text: '{docx_text}'")
            print(f"  ✅ Question content will appear in DOCX!")
        else:
            docx_text = f"Câu {i}: "
            print(f"  DOCX text: '{docx_text}'")
            print(f"  ❌ Empty question content in DOCX!")
    
    return formatted_questions

def create_test_docx(questions):
    """Tạo file DOCX test để kiểm tra"""
    
    print(f"\n=== CREATING TEST DOCX ===")
    
    try:
        doc = Document()
        
        # Tiêu đề
        title = doc.add_heading("ĐỀ THI TEST - KIỂM TRA FIX", level=1)
        
        # Tạo câu hỏi
        for i, question in enumerate(questions, 1):
            # Câu hỏi
            q_para = doc.add_paragraph()
            q_para.add_run(f"Câu {i}: ").bold = True
            q_para.add_run(question.get("noi_dung_cau_hoi", ""))  # ✅ Sử dụng field đúng
            
            # Đáp án
            dap_an = question.get("dap_an", {})
            for option in ["A", "B", "C", "D"]:
                if option in dap_an:
                    option_para = doc.add_paragraph()
                    option_para.add_run(f"{option}. {dap_an[option]}")
        
        # Lưu file
        filename = "test_fix_result.docx"
        doc.save(filename)
        
        print(f"✅ Test DOCX created: {filename}")
        
        # Kiểm tra nội dung
        check_test_docx(filename)
        
    except Exception as e:
        print(f"❌ Error creating test DOCX: {e}")

def check_test_docx(filename):
    """Kiểm tra nội dung file DOCX test"""
    
    print(f"\n=== CHECKING TEST DOCX: {filename} ===")
    
    try:
        doc = Document(filename)
        
        question_count = 0
        empty_questions = 0
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text.startswith("Câu "):
                question_count += 1
                print(f"Found: {text}")
                
                # Kiểm tra câu hỏi trống
                if text.endswith(":") and len(text.split(":")[1].strip()) == 0:
                    empty_questions += 1
                    print(f"  ❌ EMPTY!")
                else:
                    print(f"  ✅ HAS CONTENT!")
        
        print(f"\n=== FINAL RESULT ===")
        print(f"Total questions: {question_count}")
        print(f"Empty questions: {empty_questions}")
        
        if empty_questions == 0:
            print("🎉 SUCCESS: ALL QUESTIONS HAVE CONTENT!")
            print("🎉 FIX WORKED!")
        else:
            print(f"❌ FAILED: {empty_questions} empty questions")
            print("❌ FIX DID NOT WORK!")
            
    except Exception as e:
        print(f"❌ Error checking DOCX: {e}")

if __name__ == "__main__":
    questions = simulate_question_processing()
    create_test_docx(questions)
