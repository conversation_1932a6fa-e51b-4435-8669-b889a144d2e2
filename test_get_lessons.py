#!/usr/bin/env python3
"""
Test script để lấy danh sách lessons có sẵn trong database
"""
import requests

def test_get_lessons():
    """Test lấy danh sách lessons"""
    try:
        print("Testing get lessons API...")
        
        # Test endpoint để lấy danh sách lessons
        url = "http://localhost:8000/api/v1/lessons"
        
        print(f"URL: {url}")
        
        response = requests.get(url)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
            
            # Nếu có lessons, lấy lesson_id đầu tiên để test
            if isinstance(data, list) and len(data) > 0:
                first_lesson = data[0]
                print(f"First lesson: {first_lesson}")
                if 'lesson_id' in first_lesson:
                    return first_lesson['lesson_id']
                elif '_id' in first_lesson:
                    return first_lesson['_id']
                elif 'id' in first_lesson:
                    return first_lesson['id']
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

def test_search_lessons():
    """Test search lessons API"""
    try:
        print("\nTesting search lessons API...")
        
        url = "http://localhost:8000/api/v1/search/lessons"
        
        # Test với query đơn giản
        params = {"query": "hóa", "limit": 5}
        
        print(f"URL: {url}")
        print(f"Params: {params}")
        
        response = requests.get(url, params=params)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Search results: {data}")
            
            # Nếu có kết quả, lấy lesson_id đầu tiên
            if isinstance(data, list) and len(data) > 0:
                first_result = data[0]
                print(f"First search result: {first_result}")
                if 'lesson_id' in first_result:
                    return first_result['lesson_id']
                elif '_id' in first_result:
                    return first_result['_id']
                elif 'id' in first_result:
                    return first_result['id']
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

def test_exam_with_real_lesson_id(lesson_id):
    """Test exam generation với lesson_id thực"""
    try:
        print(f"\nTesting exam generation with real lesson_id: {lesson_id}")
        
        url = "http://localhost:8000/api/v1/exam/generate-exam"
        
        payload = {
            "exam_id": "test_exam_real",
            "ten_truong": "Trường THPT Test",
            "mon_hoc": "Hóa học",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "lesson_id": lesson_id,
                    "yeu_cau_can_dat": "Hiểu biết cơ bản về cấu tạo nguyên tử",
                    "muc_do": [
                        { 
                            "loai": "Nhận biết", 
                            "so_cau": 2, 
                            "loai_cau": ["TN"] 
                        }
                    ]
                }
            ]
        }
        
        print(f"Payload: {payload}")
        
        response = requests.post(url, json=payload)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing Lesson APIs ===")
    
    # Test 1: Lấy danh sách lessons
    lesson_id = test_get_lessons()
    
    # Test 2: Nếu không có, thử search
    if not lesson_id:
        lesson_id = test_search_lessons()
    
    # Test 3: Nếu có lesson_id, test exam generation
    if lesson_id:
        print(f"\nFound lesson_id: {lesson_id}")
        success = test_exam_with_real_lesson_id(lesson_id)
        if success:
            print("✅ Exam generation successful!")
        else:
            print("❌ Exam generation failed!")
    else:
        print("❌ No lesson_id found in database!")
