#!/usr/bin/env python3
"""
Script để kiểm tra nội dung file DOCX được tạo ra
"""
import os
import glob
from docx import Document

def check_latest_docx():
    """Kiểm tra file DOCX mới nhất được tạo"""
    
    # Tìm file DOCX mới nhất
    docx_files = glob.glob("*.docx")
    if not docx_files:
        print("Khong tim thay file DOCX nao")
        return
    
    # Lấy file mới nhất
    latest_file = max(docx_files, key=os.path.getctime)
    print(f"Checking file: {latest_file}")
    
    try:
        # Đọc file DOCX
        doc = Document(latest_file)
        
        print("=" * 50)
        print("NOI DUNG FILE DOCX:")
        print("=" * 50)
        
        question_count = 0
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:
                print(f"Paragraph {i+1}: {text}")
                
                # Đếm câu hỏi
                if text.startswith("Câu "):
                    question_count += 1
                    print(f"  -> QUESTION {question_count} DETECTED")
        
        print("=" * 50)
        print(f"TONG SO CAU HOI PHAT HIEN: {question_count}")
        print("=" * 50)
        
        # Kiểm tra xem có câu hỏi trống không
        empty_questions = 0
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text.startswith("Câu ") and text.endswith(":"):
                # Câu hỏi chỉ có "Câu X:" mà không có nội dung
                empty_questions += 1
                print(f"EMPTY QUESTION FOUND: {text}")
        
        if empty_questions > 0:
            print(f"CANH BAO: Co {empty_questions} cau hoi TRONG!")
        else:
            print("TAT CA CAU HOI DIEU CO NOI DUNG!")
            
    except Exception as e:
        print(f"Loi khi doc file DOCX: {e}")

if __name__ == "__main__":
    check_latest_docx()
