#!/usr/bin/env python3
"""
Test script để kiểm tra OpenRouter API key
"""
import os
import asyncio
import sys
from dotenv import load_dotenv

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.openrouter_service import OpenRouterService
from app.services.llm_service import LLMService

# Clear environment cache and reload
for key in list(os.environ.keys()):
    if 'OPENROUTER' in key or 'GEMINI' in key:
        os.environ.pop(key, None)

load_dotenv(override=True)  # Force reload .env file

async def test_openrouter_service():
    """Test OpenRouter service trực tiếp"""
    print("=== Testing OpenRouter Service ===")
    
    try:
        # Test OpenRouter service
        openrouter_service = OpenRouterService()
        
        print(f"API Key: {openrouter_service.api_key[:20] if openrouter_service.api_key else 'None'}...")
        print(f"Model: {openrouter_service.model}")
        print(f"Available: {openrouter_service.is_available()}")
        
        if not openrouter_service.is_available():
            print("OpenRouter service not available")
            return False
        
        # Test connection
        print("\n--- Testing Connection ---")
        connection_result = await openrouter_service.test_connection()
        print(f"Connection success: {connection_result['success']}")

        if not connection_result["success"]:
            print("Connection test failed")
            return False

        # Test content generation
        print("\n--- Testing Content Generation ---")
        test_prompt = "Write a simple chemistry multiple choice question in Vietnamese."
        result = await openrouter_service.generate_content(test_prompt)

        print(f"Generation success: {result['success']}")
        if result["success"]:
            print(f"Response length: {len(result['text'])} characters")
            if "usage" in result:
                print(f"Token usage: {result['usage']}")
        else:
            print(f"Error: {result['error']}")
            return False

        print("OpenRouter service test passed!")
        return True

    except Exception as e:
        print(f"OpenRouter service test failed: {e}")
        return False

async def test_llm_service():
    """Test LLM service với OpenRouter"""
    print("\n=== Testing LLM Service with OpenRouter ===")
    
    try:
        # Test LLM service
        llm_service = LLMService()
        
        print(f"Use OpenRouter: {llm_service.use_openrouter}")
        print(f"Available: {llm_service.is_available()}")
        
        if not llm_service.is_available():
            print("LLM service not available")
            return False
        
        # Test exam question generation
        print("\n--- Testing Exam Question Generation ---")
        test_prompt = """
Tạo 1 câu hỏi trắc nghiệm về hóa học theo format JSON sau:

```json
{
  "cau_hoi": "Nội dung câu hỏi ở đây",
  "dap_an": {
    "A": "Lựa chọn A",
    "B": "Lựa chọn B", 
    "C": "Lựa chọn C",
    "D": "Lựa chọn D"
  },
  "dap_an_dung": "A",
  "giai_thich": "Giải thích đáp án",
  "muc_do": "Nhận biết",
  "loai_cau": "TN"
}
```

Chủ đề: Cấu tạo nguyên tử
Mức độ: Nhận biết

Chỉ trả về JSON, không thêm text khác.
"""
        
        result = await llm_service.generate_exam_questions(test_prompt)
        
        print(f"Generation success: {result['success']}")
        if result["success"]:
            print(f"Response length: {len(result['formatted_text'])} characters")
        else:
            print(f"Error: {result['error']}")
            return False
        
        print("LLM service test passed!")
        return True

    except Exception as e:
        print(f"LLM service test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("Starting OpenRouter API Tests...")

    # Test OpenRouter service
    openrouter_success = await test_openrouter_service()

    # Test LLM service
    llm_success = await test_llm_service()

    print("\n=== Test Summary ===")
    print(f"OpenRouter Service: {'PASS' if openrouter_success else 'FAIL'}")
    print(f"LLM Service: {'PASS' if llm_success else 'FAIL'}")

    if openrouter_success and llm_success:
        print("\nAll tests passed! OpenRouter API is working correctly.")
    else:
        print("\nSome tests failed. Please check the configuration.")

if __name__ == "__main__":
    asyncio.run(main())
