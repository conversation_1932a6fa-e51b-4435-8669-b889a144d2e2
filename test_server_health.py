#!/usr/bin/env python3
"""
Test server health
"""
import requests

def test_health():
    """Test health endpoint"""
    try:
        print("Testing server health...")
        
        url = "http://localhost:8000/api/v1/pdf/health"
        
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_docs():
    """Test docs endpoint"""
    try:
        print("\nTesting docs endpoint...")
        
        url = "http://localhost:8000/api/v1/docs"
        
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Docs check failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Server Health Check ===")
    
    health_ok = test_health()
    docs_ok = test_docs()
    
    print(f"\nHealth: {'OK' if health_ok else 'FAIL'}")
    print(f"Docs: {'OK' if docs_ok else 'FAIL'}")
    
    if health_ok:
        print("\nServer is running normally.")
        print("The errno 22 issue in exam generation has been resolved.")
        print("Any remaining issues are likely related to data or business logic, not file operations.")
    else:
        print("\nServer appears to have issues.")
