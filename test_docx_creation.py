#!/usr/bin/env python3
"""
Test script để kiểm tra việc tạo file DOCX trực tiếp
"""
import os
import tempfile
from docx import Document
from datetime import datetime

def test_simple_docx_creation():
    """Test tạo file DOCX đơn giản"""
    try:
        print("Testing simple DOCX creation...")
        
        # Tạo document mới
        doc = Document()
        doc.add_heading('Test Document', 0)
        doc.add_paragraph('This is a test document.')
        
        # Tạo filename
        temp_dir = tempfile.gettempdir()
        filename = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(temp_dir, filename)
        
        print(f"Temp directory: {temp_dir}")
        print(f"Filename: {filename}")
        print(f"Full path: {filepath}")
        print(f"Directory exists: {os.path.exists(temp_dir)}")
        print(f"Directory writable: {os.access(temp_dir, os.W_OK)}")
        
        # Lư<PERSON> file
        print("Saving document...")
        doc.save(filepath)
        
        # Kiể<PERSON> tra file đã được tạo
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"[OK] File created successfully!")
            print(f"File size: {file_size} bytes")

            # Xóa file test
            os.remove(filepath)
            print("[OK] Test file cleaned up")
            return True
        else:
            print("[ERROR] File was not created")
            return False

    except Exception as e:
        print(f"[ERROR] Error: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_filename_with_special_chars():
    """Test tạo file với tên có ký tự đặc biệt"""
    try:
        print("\nTesting filename with special characters...")
        
        doc = Document()
        doc.add_heading('Test với ký tự đặc biệt', 0)
        
        temp_dir = tempfile.gettempdir()
        # Tên file có ký tự tiếng Việt và đặc biệt
        filename = f"test_Hóa_học_12_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(temp_dir, filename)
        
        print(f"Testing filename: {filename}")
        print(f"Full path: {filepath}")
        
        doc.save(filepath)
        
        if os.path.exists(filepath):
            print("[OK] File with special chars created successfully!")
            os.remove(filepath)
            print("[OK] Test file cleaned up")
            return True
        else:
            print("[ERROR] File with special chars was not created")
            return False

    except Exception as e:
        print(f"[ERROR] Error with special chars: {e}")
        print(f"Error type: {type(e)}")
        return False

def test_long_filename():
    """Test tạo file với tên dài"""
    try:
        print("\nTesting long filename...")
        
        doc = Document()
        doc.add_heading('Test Long Filename', 0)
        
        temp_dir = tempfile.gettempdir()
        # Tên file rất dài
        long_name = "a" * 200  # 200 ký tự
        filename = f"test_{long_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(temp_dir, filename)
        
        print(f"Testing long filename length: {len(filename)}")
        print(f"Full path length: {len(filepath)}")
        
        doc.save(filepath)
        
        if os.path.exists(filepath):
            print("[OK] File with long name created successfully!")
            os.remove(filepath)
            print("[OK] Test file cleaned up")
            return True
        else:
            print("[ERROR] File with long name was not created")
            return False

    except Exception as e:
        print(f"[ERROR] Error with long filename: {e}")
        print(f"Error type: {type(e)}")
        return False

if __name__ == "__main__":
    print("=== DOCX Creation Tests ===")
    
    results = []
    results.append(test_simple_docx_creation())
    results.append(test_filename_with_special_chars())
    results.append(test_long_filename())
    
    print(f"\n=== Results ===")
    print(f"Simple creation: {'[OK]' if results[0] else '[ERROR]'}")
    print(f"Special chars: {'[OK]' if results[1] else '[ERROR]'}")
    print(f"Long filename: {'[OK]' if results[2] else '[ERROR]'}")

    if all(results):
        print("[SUCCESS] All tests passed!")
    else:
        print("[WARNING] Some tests failed!")
