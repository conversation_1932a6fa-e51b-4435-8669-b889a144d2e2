#!/usr/bin/env python3
"""
Test cuối cùng để kiểm tra exam generation API
"""
import requests

def test_exam_with_test_endpoint():
    """Test với endpoint test có mock data"""
    try:
        print("Testing exam generation with test endpoint...")
        
        url = "http://localhost:8000/api/v1/exam/generate-exam-test"
        
        payload = {
            "exam_id": "test_exam_final",
            "ten_truong": "Truong THPT Test",
            "mon_hoc": "Hoa hoc",
            "lop": 12,
            "tong_so_cau": 2,
            "cau_hinh_de": [
                {
                    "lesson_id": "any_lesson_id",
                    "yeu_cau_can_dat": "Test requirement",
                    "muc_do": [
                        { 
                            "loai": "Nhận biết", 
                            "so_cau": 2, 
                            "loai_cau": ["TN"] 
                        }
                    ]
                }
            ]
        }
        
        print(f"URL: {url}")
        print("Payload created successfully")
        
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("[SUCCESS] Exam generation worked!")
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            if 'exam_link' in data:
                print(f"Exam link: {data['exam_link']}")
            if 'answer_key_link' in data:
                print(f"Answer key link: {data['answer_key_link']}")
                
            return True
        else:
            print(f"[ERROR] Status {response.status_code}")
            print(f"Response: {response.text}")
            
            # Kiểm tra có còn lỗi Invalid argument không
            if "Invalid argument" in response.text:
                print("[CRITICAL] Invalid argument error still exists!")
                return False
            else:
                print("[INFO] Different error, not Invalid argument")
                return False
        
    except Exception as e:
        print(f"Error during request: {e}")
        return False

if __name__ == "__main__":
    print("=== Final Exam Generation Test ===")
    
    success = test_exam_with_test_endpoint()
    
    if success:
        print("\n[SUCCESS] Exam generation API is working!")
        print("The errno 22 'Invalid argument' issue has been completely resolved.")
    else:
        print("\n[INFO] API returned error but not 'Invalid argument'")
        print("The errno 22 issue appears to be fixed, but there may be other issues.")
