#!/usr/bin/env python3
"""
Test script để kiểm tra exam generation API
"""
import requests
import json

def test_exam_generation():
    """Test exam generation API"""
    url = "http://localhost:8000/api/v1/exam/generate-exam"
    
    # Test payload với format mới
    payload = {
        "exam_id": "test_exam_001",
        "ten_truong": "Trường THPT Test",
        "mon_hoc": "Hóa học",
        "lop": 12,
        "tong_so_cau": 2,
        "cau_hinh_de": [
            {
                "lesson_id": "lesson_01_01",
                "yeu_cau_can_dat": "<PERSON><PERSON><PERSON> biết cơ bản về cấu tạo nguyên tử",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing exam generation API...")
        print(f"URL: {url}")
        print("Payload created successfully")

        response = requests.post(url, json=payload, timeout=60)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("API hoat dong binh thuong!")
            print("File DOCX da duoc tao thanh cong!")

            # Kiểm tra headers để xem thông tin về số câu hỏi
            headers = response.headers
            total_questions = headers.get('X-Total-Questions', 'Unknown')
            test_mode = headers.get('X-Test-Mode', 'false')

            print(f"Total questions generated: {total_questions}")
            print(f"Test mode: {test_mode}")
            print(f"Content-Type: {headers.get('Content-Type', 'Unknown')}")
            print(f"File size: {len(response.content)} bytes")

            return True
        else:
            print(f"API tra ve loi: {response.status_code}")
            try:
                print("Response:", response.json())
            except:
                print("Response text:", response.text[:500])
            return False

    except Exception as e:
        print(f"Loi khi test API: {e}")
        return False

if __name__ == "__main__":
    test_exam_generation()
