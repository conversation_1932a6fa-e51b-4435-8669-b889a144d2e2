#!/usr/bin/env python3
"""
Test script để kiểm tra logic xử lý câu hỏi từ Gemini response
"""
import json
import re
import sys
import os

# Add app to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_question_parsing():
    """Test parsing logic với response thật từ Gemini"""
    
    # Sử dụng response thật từ file gemini_response.txt
    try:
        with open("gemini_response.txt", "r", encoding="utf-8") as f:
            response_text = f.read()
    except FileNotFoundError:
        print("Khong tim thay file gemini_response.txt")
        return
    
    print("=== TESTING QUESTION PARSING LOGIC ===")
    print(f"Response length: {len(response_text)} characters")
    
    # Test regex pattern từ exam_generation_service.py
    json_pattern = r'```json\s*(\[.*?\])\s*```'
    matches = re.findall(json_pattern, response_text, re.DOTALL)
    
    print(f"Found {len(matches)} JSON blocks")
    
    if matches:
        for i, match in enumerate(matches):
            print(f"\n--- JSON Block {i+1} ---")
            try:
                questions = json.loads(match)
                print(f"Successfully parsed {len(questions)} questions")
                
                for j, q in enumerate(questions):
                    print(f"\nQuestion {j+1}:")
                    print(f"  Keys: {list(q.keys())}")
                    
                    # Kiểm tra các field quan trọng
                    cau_hoi = q.get('cau_hoi', 'MISSING')
                    dap_an = q.get('dap_an', 'MISSING')
                    dap_an_dung = q.get('dap_an_dung', 'MISSING')
                    
                    print(f"  cau_hoi: '{cau_hoi}'")
                    print(f"  dap_an: {dap_an}")
                    print(f"  dap_an_dung: '{dap_an_dung}'")
                    
                    # Kiểm tra xem câu hỏi có trống không
                    if not cau_hoi or cau_hoi == 'MISSING' or len(cau_hoi.strip()) == 0:
                        print(f"  ❌ EMPTY QUESTION DETECTED!")
                    else:
                        print(f"  ✅ Question has content")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON parse error: {e}")
    else:
        print("❌ No JSON blocks found")
        
        # Try fallback parsing
        print("\nTrying fallback parsing...")
        start_idx = response_text.find("[")
        if start_idx != -1:
            bracket_count = 0
            end_idx = -1
            for i in range(start_idx, len(response_text)):
                if response_text[i] == '[':
                    bracket_count += 1
                elif response_text[i] == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_idx = i
                        break
            
            if end_idx != -1:
                json_text = response_text[start_idx:end_idx + 1]
                print(f"Extracted JSON: {json_text[:200]}...")
                
                try:
                    questions = json.loads(json_text)
                    print(f"✅ Fallback successful: {len(questions)} questions")
                    
                    for j, q in enumerate(questions):
                        cau_hoi = q.get('cau_hoi', 'MISSING')
                        print(f"Question {j+1} content: '{cau_hoi}'")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Fallback failed: {e}")

def test_docx_generation_logic():
    """Test logic tạo DOCX với dữ liệu mẫu"""
    
    print("\n=== TESTING DOCX GENERATION LOGIC ===")
    
    # Dữ liệu câu hỏi mẫu (giống như từ Gemini)
    sample_questions = [
        {
            "cau_hoi": "Hạt nào mang điện tích dương trong nguyên tử?",
            "dap_an": {
                "A": "Proton",
                "B": "Neutron", 
                "C": "Electron",
                "D": "Proton và Neutron"
            },
            "dap_an_dung": "A",
            "giai_thich": "Proton mang điện tích dương, neutron không mang điện, electron mang điện tích âm.",
            "muc_do": "Nhận biết",
            "loai_cau": "TN"
        },
        {
            "cau_hoi": "Hạt nào có khối lượng gần bằng 0 so với proton và neutron trong nguyên tử?",
            "dap_an": {
                "A": "Proton",
                "B": "Neutron",
                "C": "Electron", 
                "D": "Proton và Neutron"
            },
            "dap_an_dung": "C",
            "giai_thich": "Khối lượng của electron rất nhỏ so với proton và neutron.",
            "muc_do": "Nhận biết",
            "loai_cau": "TN"
        }
    ]
    
    print(f"Sample data has {len(sample_questions)} questions")
    
    # Test field access
    for i, q in enumerate(sample_questions):
        print(f"\nQuestion {i+1}:")
        
        # Test các cách access field khác nhau
        cau_hoi_1 = q.get('cau_hoi')
        cau_hoi_2 = q.get('noi_dung') 
        cau_hoi_3 = q.get('de_bai')
        
        print(f"  q.get('cau_hoi'): '{cau_hoi_1}'")
        print(f"  q.get('noi_dung'): '{cau_hoi_2}'")
        print(f"  q.get('de_bai'): '{cau_hoi_3}'")
        
        # Kiểm tra field nào có data
        if cau_hoi_1:
            print(f"  ✅ 'cau_hoi' field has data")
        if cau_hoi_2:
            print(f"  ✅ 'noi_dung' field has data")
        if cau_hoi_3:
            print(f"  ✅ 'de_bai' field has data")

if __name__ == "__main__":
    test_question_parsing()
    test_docx_generation_logic()
